# 通用图片上传下载接口文档

## 概述

本项目提供了一套完整的图片文件上传、下载和管理接口，支持单个和批量上传，具有完善的文件验证、存储管理和缓存机制。

## 配置

图片存储路径通过配置项 `app.file-storage.base-path` 设置：

- **开发环境**: `D:\upload`
- **生产环境**: `/data/upload`

## API 接口

### 1. 单个图片上传

**接口地址**: `POST /api/files/upload`

**请求参数**:
- `file` (MultipartFile, 必需): 上传的图片文件
- `recordId` (Long, 可选): 关联的记录ID

**支持的图片格式**:
- JPG/JPEG
- PNG
- GIF
- WebP

**文件大小限制**: 最大 10MB

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1234567890,
    "url": "/api/files/download/1234567890",
    "originalFilename": "example.jpg",
    "fileSize": 1024000,
    "contentType": "image/jpeg",
    "md5Hash": "d41d8cd98f00b204e9800998ecf8427e"
  }
}
```

### 2. 批量图片上传

**接口地址**: `POST /api/files/upload/batch`

**请求参数**:
- `files` (MultipartFile[], 必需): 上传的图片文件数组（最多10个）
- `recordId` (Long, 可选): 关联的记录ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "success": [
      {
        "id": 1234567890,
        "url": "/api/files/download/1234567890",
        "originalFilename": "example1.jpg",
        "fileSize": 1024000,
        "contentType": "image/jpeg",
        "md5Hash": "d41d8cd98f00b204e9800998ecf8427e"
      }
    ],
    "errors": [
      "文件 2: 不支持的图片格式"
    ],
    "successCount": 1,
    "errorCount": 1
  }
}
```

### 3. 图片下载

**接口地址**: `GET /api/files/download/{id}`

**路径参数**:
- `id` (Long): 图片ID

**响应**: 直接返回图片文件流，包含适当的缓存头信息

**缓存策略**:
- 缓存时间: 7天
- ETag: 基于文件MD5值
- 支持条件请求

### 4. 获取图片信息

**接口地址**: `GET /api/files/info/{id}`

**路径参数**:
- `id` (Long): 图片ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1234567890,
    "recordId": 123,
    "imageType": "SOURCE",
    "originalFilename": "example.jpg",
    "storedFilename": "2024/01/15/uuid-filename.jpg",
    "fileSize": 1024000,
    "contentType": "image/jpeg",
    "imageUrl": "2024/01/15/uuid-filename.jpg",
    "md5Hash": "d41d8cd98f00b204e9800998ecf8427e",
    "createdAt": "2024-01-15T10:30:00",
    "updatedAt": "2024-01-15T10:30:00",
    "downloadUrl": "/api/files/download/1234567890"
  }
}
```

### 5. 删除图片

**接口地址**: `DELETE /api/files/delete/{id}`

**路径参数**:
- `id` (Long): 图片ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": "图片删除成功"
}
```

### 6. 分页查询图片列表

**接口地址**: `POST /api/files/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "query": "搜索关键词",
  "sortField": "created_at",
  "sortOrder": "desc"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "id": 1234567890,
      "recordId": 123,
      "imageType": "SOURCE",
      "originalFilename": "example.jpg",
      "fileSize": 1024000,
      "contentType": "image/jpeg",
      "createdAt": "2024-01-15T10:30:00"
    }
  ],
  "total": 100,
  "pageNum": 1,
  "pageSize": 10
}
```

## 文件存储结构

文件按照以下目录结构存储：

```
{app.file-storage.base-path}/
├── 2024/
│   ├── 01/
│   │   ├── 15/
│   │   │   ├── [recordId]/
│   │   │   │   └── uuid-filename.jpg
│   │   │   └── uuid-filename.jpg
```

## 错误码说明

- `0`: 成功
- `1`: 失败

常见错误信息：
- "上传文件不能为空"
- "不支持的图片格式，仅支持 JPG、PNG、GIF、WebP"
- "文件大小不能超过10MB"
- "批量上传最多支持10个文件"
- "图片不存在"

## 安全性

1. **文件类型验证**: 严格验证MIME类型
2. **文件大小限制**: 防止大文件攻击
3. **路径安全**: 使用UUID生成文件名，防止路径遍历
4. **访问控制**: 下载接口无需认证，其他接口需要认证

## 使用示例

### JavaScript/前端上传示例

```javascript
// 单个文件上传
const uploadSingle = async (file, recordId) => {
  const formData = new FormData();
  formData.append('file', file);
  if (recordId) {
    formData.append('recordId', recordId);
  }
  
  const response = await fetch('/api/files/upload', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};

// 批量文件上传
const uploadBatch = async (files, recordId) => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });
  if (recordId) {
    formData.append('recordId', recordId);
  }
  
  const response = await fetch('/api/files/upload/batch', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};
```

### cURL 示例

```bash
# 单个文件上传
curl -X POST \
  -F "file=@/path/to/image.jpg" \
  -F "recordId=123" \
  http://localhost:6956/api/files/upload

# 图片下载
curl -O http://localhost:6956/api/files/download/1234567890

# 获取图片信息
curl http://localhost:6956/api/files/info/1234567890
```

## 注意事项

1. 下载接口 `/api/files/download/**` 已配置为无需认证
2. 文件存储使用雪花算法生成唯一ID
3. 支持MD5去重检测
4. 自动生成缩略图（如需要可扩展）
5. 支持逻辑删除，物理文件暂时保留
