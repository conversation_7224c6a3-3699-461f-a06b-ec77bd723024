package com.kedish.xyhelper_fox.service;

import com.kedish.xyhelper_fox.utils.I18nUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * I18n服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class I18nServiceTest {

    @Resource
    private I18nService i18nService;

    @Test
    public void testGetSupportedLanguages() {
        List<Map<String, String>> languages = i18nService.getSupportedLanguageInfo();
        assertNotNull(languages);
        assertEquals(2, languages.size());
        
        // 验证中文语言信息
        Map<String, String> zhLang = languages.stream()
            .filter(lang -> "zh".equals(lang.get("code")))
            .findFirst()
            .orElse(null);
        assertNotNull(zhLang);
        assertEquals("中文", zhLang.get("name"));
        
        // 验证英文语言信息
        Map<String, String> enLang = languages.stream()
            .filter(lang -> "en".equals(lang.get("code")))
            .findFirst()
            .orElse(null);
        assertNotNull(enLang);
        assertEquals("English", enLang.get("name"));
    }

    @Test
    public void testValidateLanguageCode() {
        // 测试有效的语言代码
        assertTrue(i18nService.isValidLanguageCode("zh"));
        assertTrue(i18nService.isValidLanguageCode("en"));
        assertTrue(i18nService.isValidLanguageCode("zh-cn"));
        assertTrue(i18nService.isValidLanguageCode("en-us"));
        
        // 测试无效的语言代码
        assertFalse(i18nService.isValidLanguageCode("ja"));
        assertFalse(i18nService.isValidLanguageCode("fr"));
        assertFalse(i18nService.isValidLanguageCode(""));
        assertFalse(i18nService.isValidLanguageCode(null));
    }

    @Test
    public void testGetMessages() {
        List<String> keys = Arrays.asList("common.success", "common.fail", "user.login.success");
        
        // 测试中文消息
        Map<String, String> zhMessages = i18nService.getMessages(keys, Locale.SIMPLIFIED_CHINESE);
        assertNotNull(zhMessages);
        assertEquals("操作成功", zhMessages.get("common.success"));
        assertEquals("操作失败", zhMessages.get("common.fail"));
        assertEquals("登录成功", zhMessages.get("user.login.success"));
        
        // 测试英文消息
        Map<String, String> enMessages = i18nService.getMessages(keys, Locale.ENGLISH);
        assertNotNull(enMessages);
        assertEquals("Operation successful", enMessages.get("common.success"));
        assertEquals("Operation failed", enMessages.get("common.fail"));
        assertEquals("Login successful", enMessages.get("user.login.success"));
    }

    @Test
    public void testHasMessage() {
        // 测试存在的消息键
        assertTrue(i18nService.hasMessage("common.success", Locale.SIMPLIFIED_CHINESE));
        assertTrue(i18nService.hasMessage("common.success", Locale.ENGLISH));
        
        // 测试不存在的消息键
        assertFalse(i18nService.hasMessage("non.existent.key", Locale.SIMPLIFIED_CHINESE));
        assertFalse(i18nService.hasMessage("non.existent.key", Locale.ENGLISH));
    }

    @Test
    public void testGetLanguageDisplayName() {
        String zhDisplayName = i18nService.getLanguageDisplayName("zh");
        String enDisplayName = i18nService.getLanguageDisplayName("en");
        
        assertNotNull(zhDisplayName);
        assertNotNull(enDisplayName);
        
        // 显示名称应该不为空
        assertFalse(zhDisplayName.isEmpty());
        assertFalse(enDisplayName.isEmpty());
    }

    @Test
    public void testI18nUtils() {
        // 测试工具类方法
        assertTrue(I18nUtils.isSupportedLanguage("zh"));
        assertTrue(I18nUtils.isSupportedLanguage("en"));
        assertFalse(I18nUtils.isSupportedLanguage("ja"));
        
        // 测试Locale获取
        assertEquals(Locale.SIMPLIFIED_CHINESE, I18nUtils.getLocaleByCode("zh"));
        assertEquals(Locale.ENGLISH, I18nUtils.getLocaleByCode("en"));
        assertEquals(Locale.SIMPLIFIED_CHINESE, I18nUtils.getLocaleByCode("invalid"));
    }
}
