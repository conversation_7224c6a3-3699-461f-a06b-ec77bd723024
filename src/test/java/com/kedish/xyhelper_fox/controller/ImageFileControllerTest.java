package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.service.ImageStorageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 图片文件控制器测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class ImageFileControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ImageStorageService imageStorageService;

    private MockMvc mockMvc;

    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void testUploadImageValidation() throws Exception {
        setUp();

        // 测试空文件上传
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "empty.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                new byte[0]
        );

        mockMvc.perform(multipart("/api/files/upload")
                .file(emptyFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("上传文件不能为空"));
    }

    @Test
    public void testUploadImageInvalidType() throws Exception {
        setUp();

        // 测试不支持的文件类型
        MockMultipartFile invalidFile = new MockMultipartFile(
                "file",
                "test.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "test content".getBytes()
        );

        mockMvc.perform(multipart("/api/files/upload")
                .file(invalidFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("不支持的图片格式，仅支持 JPG、PNG、GIF、WebP"));
    }

    @Test
    public void testUploadImageSuccess() throws Exception {
        setUp();

        // 创建一个简单的测试图片文件
        byte[] imageContent = createSimpleImageBytes();
        MockMultipartFile validFile = new MockMultipartFile(
                "file",
                "test.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                imageContent
        );

        mockMvc.perform(multipart("/api/files/upload")
                .file(validFile)
                .param("recordId", "123"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.originalFilename").value("test.jpg"))
                .andExpect(jsonPath("$.data.contentType").value("image/jpeg"))
                .andExpect(jsonPath("$.data.fileSize").value(imageContent.length));
    }

    @Test
    public void testBatchUploadValidation() throws Exception {
        setUp();

        // 测试批量上传文件数量限制
        MockMultipartFile[] files = new MockMultipartFile[11];
        for (int i = 0; i < 11; i++) {
            files[i] = new MockMultipartFile(
                    "files",
                    "test" + i + ".jpg",
                    MediaType.IMAGE_JPEG_VALUE,
                    createSimpleImageBytes()
            );
        }

        mockMvc.perform(multipart("/api/files/upload/batch")
                .file(files[0])
                .file(files[1])
                .file(files[2])
                .file(files[3])
                .file(files[4])
                .file(files[5])
                .file(files[6])
                .file(files[7])
                .file(files[8])
                .file(files[9])
                .file(files[10]))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("批量上传最多支持10个文件"));
    }

    @Test
    public void testGetImageInfoNotFound() throws Exception {
        setUp();

        mockMvc.perform(get("/api/files/info/999999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("图片不存在"));
    }

    @Test
    public void testListImages() throws Exception {
        setUp();

        mockMvc.perform(post("/api/files/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"pageNum\":1,\"pageSize\":10}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    /**
     * 创建一个简单的测试图片字节数组
     * 这里创建一个最小的JPEG文件头
     */
    private byte[] createSimpleImageBytes() {
        // 这是一个最小的JPEG文件头
        return new byte[]{
                (byte) 0xFF, (byte) 0xD8, (byte) 0xFF, (byte) 0xE0,
                0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
                0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00,
                (byte) 0xFF, (byte) 0xD9
        };
    }
}
