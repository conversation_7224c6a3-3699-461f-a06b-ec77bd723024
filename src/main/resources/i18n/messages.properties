# ?????? (??)
# ????
common.success=????
common.fail=????
common.error=????
common.param.error=????
common.unauthorized=?????
common.forbidden=????
common.confirm=??
common.cancel=??
common.save=??
common.delete=??
common.edit=??
common.add=??
common.search=??
common.reset=??
common.submit=??
common.loading=???...
common.close=??
common.refresh=??
common.export=??
common.import=??
common.upload=??
common.download=??
common.preview=??
common.settings=??
common.help=??
common.about=??
common.version=??
common.language=??
common.theme=??

# ????
user.login.success=????
user.login.fail=????
user.logout.success=????
user.register.success=????
user.register.fail=????
user.password.change.success=??????
user.password.change.fail=??????
user.not.found=?????
user.token.expired=?????
user.not.admin=??????????
user.username=???
user.password=??
user.email=??
user.phone=???
user.profile=????
user.avatar=??
user.nickname=??
user.invalid.credentials=????????
user.account.locked=??????
user.account.expired=?????
user.permission.denied=????

# ????
config.get.fail=??????
config.update.success=??????
config.update.fail=??????
config.not.found=?????
config.invalid.value=?????

# ????
email.send.success=??????
email.send.fail=??????
email.code.invalid=?????
email.code.expired=??????
email.code.sent=??????
email.invalid.format=??????

# ????
file.upload.success=??????
file.upload.fail=??????
file.not.found=?????
file.size.exceeded=????????
file.type.not.supported=???????
file.download.success=??????
file.download.fail=??????

# ????
system.maintenance=?????
system.busy=??????????
system.error=????
system.timeout=????
system.service.unavailable=?????

# ????
car.not.found=?????
car.access.denied=???????
car.limit.exceeded=??????????
car.status.invalid=??????

# ????
draw.generation.success=??????
draw.generation.fail=??????
draw.limit.exceeded=????????
draw.prompt.invalid=???????
draw.style.not.supported=???????

# ?????
activation.code.invalid=?????
activation.code.used=??????
activation.code.expired=??????
activation.code.exchange.success=???????
activation.code.not.found=??????
activation.code.insufficient.balance=????

# ?????
i18n.language.switch.success=??????
i18n.language.switch.fail=??????
i18n.language.not.supported=??????
i18n.message.not.found=?????

# ????
validation.required=???????
validation.email=??????????
validation.phone=?????????
validation.password=??????6?
validation.confirm.password=?????????
validation.min.length=????{0}???
validation.max.length=????{0}???
validation.numeric=?????
validation.url=??????URL
validation.date=????????
validation.time=????????

# ????
pagination.total=? {0} ?
pagination.page=? {0} ?
pagination.page.size=?? {0} ?
pagination.goto=???
pagination.prev=???
pagination.next=???
pagination.first=??
pagination.last=??

# API??
api.rate.limit.exceeded=API??????
api.quota.exceeded=API?????
api.key.invalid=API????
api.service.error=API????
api.timeout=API????

# ????
payment.success=????
payment.fail=????
payment.pending=?????
payment.cancelled=?????
payment.refunded=???
payment.amount.invalid=??????
payment.method.not.supported=????????
