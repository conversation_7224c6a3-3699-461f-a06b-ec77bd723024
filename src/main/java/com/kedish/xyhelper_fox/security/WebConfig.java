package com.kedish.xyhelper_fox.security;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.*;

import java.util.concurrent.TimeUnit;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private AuthInterceptor authInterceptor;

    @Value("${spring.web.resources.static-locations}")
    private String staticLocations;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源处理
        registry.addResourceHandler("/**")
                .addResourceLocations(staticLocations);

        // 独立配置某些静态资源
        registry.addResourceHandler("/assets/**")
                .addResourceLocations("classpath:/static/assets/");
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 配置路径匹配
        configurer.setUseTrailingSlashMatch(true);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedOriginPatterns("*")
                .allowedHeaders("*")
                .allowedMethods("*")
                .maxAge(3600);

        WebMvcConfigurer.super.addCorsMappings(registry);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/chatGptUser/login", "/api/chatGptUser/captcha",
                        "/api/chatGptUser/sendEmailCode", "/api/chatGptUser/checkExpiredUsers",
                        "/api/chatGptUser/register", "/api/chatShareServer/oauth",
                        "/api/chatShareServer/grok/oauth","/api/chatShareServer/claude/oauth",
                        "/api/chatGpt/car/list",
                        "/api/config/get", "/api/auditLimit",
                        "/api/claude/auditLimit", "/api/grok/auditLimit",
                        "/api/checkCarAccess", "/api/testSetSession",
                        "/api/notification/getLatest", "/api/salesPlan/page", "/api/pay/yzf/notify",
                        "/api/paymentMethod/queryAll", "/api/system/ver","/api/download-image/**",
                        "/api/files/download/**");  // 排除登录和注册接口
    }
}
